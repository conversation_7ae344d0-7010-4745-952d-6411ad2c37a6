# 项目上下文信息

- 用户要求在向量化模块和检索模块中加入知识图谱功能，需要实现图谱-向量混合检索策略，包括实体向量化、关系推理、图谱增强检索等核心功能
- 已成功新增4-7-Text2SQL检索模块.md作为第7个检索子模块，专门处理结构化数据查询需求，包含SpringAI集成方案和多数据源支持，同时更新了4-检索模块.md、README.md、0-RAG技术方案总览.md等相关文档，形成稠密向量+稀疏向量+知识图谱+结构化数据的四重混合检索架构
- 已完成Text2SQL模块的全面优化：1.完善了用户提问到自然语言回答的完整流程图；2.新增半结构化数据处理方案（JSON/XML/CSV处理和查询转换）；3.提供NoSQL数据库选型方案（MongoDB/Elasticsearch/Redis）和查询适配器；4.实现完整的SpringAI Text2SQL案例，包含表结构向量化、智能SQL生成、自然语言回答等核心功能
