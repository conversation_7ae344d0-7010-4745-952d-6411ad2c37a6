# 用户偏好设置

- 用户需要RAG技术路线规划大纲，关注提取、分块、向量化、检索、生成等核心技术模块，不需要Spring Cloud详细设计、总结性文档、测试脚本、编译运行等
- 用户选择方案A按技术功能拆分检索模块，拆分为5个子模块：CoT推理与查询理解、查询重写与优化、混合检索引擎、智能重排与优化、自我反思与优化
- 用户选择方案A深度集成方案，新增知识图谱检索子模块，要求在现有5个检索子模块基础上新增第6个子模块4-6-知识图谱检索模块.md，同时在文档提取、智能分块、向量化模块中集成知识图谱功能
- 用户要求新增text2sql检索子模块，专门处理结构化数据查询需求，要求包含SpringAI集成方式、数据存储方式，并更新相关文档，但不需要总结性文档、测试脚本、编译运行等
- 用户要求优化Text2SQL模块：1.完善流程图（用户提问-检索最相关的表-表结构信息向量化-LLM生成SQL-执行SQL-LLM生成自然语言）2.处理半结构化数据 3.NoSQL数据库选型 4.SpringAI实现Text2SQL案例
- 用户选择方案A完整集成方案实现飞书文档同步，要求实现完整的飞书API集成、支持所有飞书文档类型、包含实时同步和增量更新、集成知识图谱数据准备，但不需要总结性Markdown文档、测试脚本、编译运行等
