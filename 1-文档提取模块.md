# 文档提取模块 (Document Extraction)

## 方案说明

### 1. 技术方案概述
文档提取模块是RAG系统的数据入口，负责从多种格式的文档中提取结构化内容，为后续的分块和向量化处理提供高质量的文本数据。该模块采用多格式支持、智能预处理和结构化提取的设计理念，确保从各类文档中准确、完整地提取有价值的信息。

### 2. 核心技术特点
- **多格式支持**：支持PDF、Word、Excel、PowerPoint、图片、飞书文档等主流文档格式
- **智能OCR集成**：集成合合OCR API，处理扫描件和图片文档
- **飞书生态集成**：深度集成飞书开放平台API，支持实时文档同步和协作内容提取
- **结构化提取**：保留文档的层次结构、表格、列表等格式信息
- **质量控制**：多阶段预处理管道，确保提取内容的质量和准确性
- **元数据增强**：自动提取关键词、实体、主题等元数据信息
- **知识图谱集成**：实体识别、关系抽取，为知识图谱构建提供数据支持
- **实时同步机制**：支持飞书文档的实时变更监听和增量同步

### 3. 技术架构优势
- **可扩展性**：基于策略模式的处理器设计，易于添加新格式支持
- **高可靠性**：多层次错误处理机制
- **高性能**：流水线并行处理，支持大批量文档处理
- **智能化**：结合NLP技术进行内容理解和结构分析
- **知识驱动**：集成知识图谱技术，提升文档理解的深度和准确性

## 流程图

```mermaid
graph TD
    A[原始文档输入] --> B[格式检测阶段]
    B --> C{文档格式判断}

    C -->|PDF文档| D[PDF处理器]
    C -->|Office文档| E[Office处理器]
    C -->|图片文档| F[图片处理器]
    C -->|飞书文档| H[飞书处理器]
    C -->|其他格式| G[通用处理器]

    D --> H{PDF类型判断}
    H -->|文本型PDF| I[文本提取]
    H -->|扫描型PDF| J[OCR识别]

    E --> K[Office内容解析]
    K --> L[段落提取]
    K --> M[表格提取]
    K --> N[样式分析]

    F --> O[图像预处理]
    O --> P[OCR文本识别]
    P --> Q[版面分析]

    H --> H1[飞书API认证]
    H1 --> H2[文档类型识别]
    H2 --> H3{飞书文档类型}
    H3 -->|飞书文档| H4[文档内容提取]
    H3 -->|飞书表格| H5[表格数据提取]
    H3 -->|多维表格| H6[多维表格提取]
    H3 -->|思维导图| H7[思维导图提取]
    H3 -->|白板| H8[白板内容提取]

    I --> R[内容预处理管道]
    J --> R
    L --> R
    M --> R
    N --> R
    P --> R
    Q --> R
    H4 --> R
    H5 --> R
    H6 --> R
    H7 --> R
    H8 --> R
    G --> R

    R --> S[噪声过滤]
    S --> T[结构分析]
    T --> U[元数据增强]
    U --> U1[实体识别与抽取]
    U1 --> U2[关系识别与抽取]
    U2 --> U3[知识图谱数据准备]
    U3 --> V[结构化文档输出]

    V --> W[分块模块]

    style A fill:#e1f5fe
    style V fill:#c8e6c9
    style W fill:#fff3e0
```

## 流程步骤说明

### 阶段一：文档格式识别与路由
1. **格式检测**：通过文件扩展名、魔数和内容特征综合判断文档格式
2. **处理器选择**：根据格式类型选择对应的专用处理器
3. **配置加载**：加载格式特定的处理配置参数

### 阶段二：内容提取处理
#### PDF文档处理流程
1. **PDF类型判断**：区分文本型PDF和扫描型PDF
2. **文本型PDF**：使用PDFBox直接提取文本内容，保留页面结构
3. **扫描型PDF**：转换为图像后调用OCR API进行文字识别
4. **页面元数据**：提取页码、字体、布局等结构信息

#### Office文档处理流程
1. **文档解析**：使用Apache POI解析Word/Excel/PowerPoint文档
2. **内容分类**：区分段落、标题、表格、列表等不同内容类型
3. **样式保留**：提取字体、颜色、大小等样式信息
4. **结构重建**：重构文档的层次结构关系

#### 图片文档处理流程
1. **图像预处理**：去噪、对比度调整、倾斜校正等增强处理
2. **OCR识别**：调用合合OCR API进行文字识别
3. **版面分析**：识别文本区域、表格区域、图像区域等
4. **置信度评估**：评估识别结果的可靠性

#### 飞书文档处理流程
1. **API认证**：使用飞书开放平台API进行身份认证和权限验证
2. **文档类型识别**：识别飞书文档、表格、多维表格、思维导图、白板等类型
3. **内容提取**：
   - **飞书文档**：提取富文本内容、段落结构、样式信息
   - **飞书表格**：提取工作表数据、单元格内容、公式信息
   - **多维表格**：提取记录数据、字段定义、视图配置
   - **思维导图**：提取节点内容、层级关系、连接信息
   - **白板**：提取文本框、图形、连线等元素内容
4. **协作信息提取**：提取评论、批注、修改历史等协作内容
5. **权限信息获取**：获取文档的分享权限、协作者信息
6. **实时同步**：通过Webhook监听文档变更事件，实现增量同步

### 阶段三：内容预处理管道
1. **噪声过滤**：
   - 移除页眉页脚、水印等无关内容
   - 过滤重复内容和空白内容
   - 清理特殊字符和编码问题

2. **结构分析**：
   - 标题层级识别和分类
   - 段落分组和章节划分
   - 表格和列表结构识别

3. **元数据增强**：
   - 关键词提取（使用阿里云NLP）
   - 实体识别（人名、地名、机构名等）
   - 主题分类和语言检测
   - 可读性评分

4. **知识图谱数据准备**：
   - 实体识别与标准化
   - 关系抽取与分类
   - 实体链接与消歧
   - 知识图谱三元组生成

### 阶段四：知识图谱数据准备
1. **实体识别与标准化**：
   - 使用NER模型识别文档中的实体
   - 实体类型分类（人物、地点、组织、概念等）
   - 实体标准化和规范化处理
   - 实体置信度评估

2. **关系抽取与分类**：
   - 识别实体间的语义关系
   - 关系类型分类和标准化
   - 关系强度和置信度计算
   - 关系验证和过滤

3. **实体链接与消歧**：
   - 将识别的实体链接到知识库
   - 处理实体歧义和同名问题
   - 构建实体的唯一标识符
   - 维护实体的别名和同义词

4. **知识图谱三元组生成**：
   - 生成标准化的RDF三元组
   - 构建实体-关系-实体的知识结构
   - 为Neo4j图数据库准备数据格式
   - 质量检查和数据验证

### 阶段五：内容封装与输出
1. **结构化封装**：将提取结果封装为标准化的文档对象
2. **元数据关联**：关联原始文档信息和提取的元数据
3. **知识图谱数据关联**：关联实体、关系和知识图谱数据
4. **输出准备**：为下游分块模块准备标准化输入

## 模块概述

文档提取模块是RAG系统的数据入口，负责从多种格式的文档中提取结构化内容，为后续的分块和向量化处理提供高质量的文本数据。

## 核心功能架构

### 1. 多格式文档解析引擎

#### 1.1 PDF文档处理
```java
@Component
public class PdfProcessor implements DocumentProcessor {
    
    @Autowired
    private HeheCombOcrClient ocrClient;
    
    public DocumentContent process(InputStream inputStream, ProcessConfig config) {
        PDDocument document = PDDocument.load(inputStream);
        
        if (isTextBasedPdf(document)) {
            return extractTextContent(document);
        } else {
            return extractOcrContent(document);
        }
    }
    
    private DocumentContent extractTextContent(PDDocument document) {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setSortByPosition(true);
        
        List<PageContent> pages = new ArrayList<>();
        for (int i = 1; i <= document.getNumberOfPages(); i++) {
            stripper.setStartPage(i);
            stripper.setEndPage(i);
            
            String pageText = stripper.getText(document);
            PageContent page = PageContent.builder()
                .pageNumber(i)
                .content(pageText)
                .metadata(extractPageMetadata(document, i))
                .build();
            pages.add(page);
        }
        
        return DocumentContent.builder()
            .pages(pages)
            .documentType(DocumentType.PDF_TEXT)
            .build();
    }
    
    private DocumentContent extractOcrContent(PDDocument document) {
        List<PageContent> pages = new ArrayList<>();
        
        for (int i = 0; i < document.getNumberOfPages(); i++) {
            PDPage page = document.getPage(i);
            BufferedImage image = renderPageToImage(page);
            
            // 调用合合OCR API
            OcrResult ocrResult = ocrClient.recognizeDocument(image);
            
            PageContent pageContent = PageContent.builder()
                .pageNumber(i + 1)
                .content(ocrResult.getText())
                .layoutInfo(ocrResult.getLayoutInfo())
                .confidence(ocrResult.getConfidence())
                .build();
            pages.add(pageContent);
        }
        
        return DocumentContent.builder()
            .pages(pages)
            .documentType(DocumentType.PDF_OCR)
            .build();
    }
}
```

#### 1.2 Office文档处理
```java
@Component
public class OfficeProcessor implements DocumentProcessor {
    
    public DocumentContent processWord(InputStream inputStream) {
        XWPFDocument document = new XWPFDocument(inputStream);
        
        List<ContentBlock> blocks = new ArrayList<>();
        
        // 处理段落
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            if (!paragraph.getText().trim().isEmpty()) {
                ContentBlock block = ContentBlock.builder()
                    .type(BlockType.PARAGRAPH)
                    .content(paragraph.getText())
                    .style(extractParagraphStyle(paragraph))
                    .build();
                blocks.add(block);
            }
        }
        
        // 处理表格
        for (XWPFTable table : document.getTables()) {
            ContentBlock tableBlock = processTable(table);
            blocks.add(tableBlock);
        }
        
        return DocumentContent.builder()
            .blocks(blocks)
            .documentType(DocumentType.WORD)
            .build();
    }
    
    private ContentBlock processTable(XWPFTable table) {
        List<List<String>> tableData = new ArrayList<>();
        
        for (XWPFTableRow row : table.getRows()) {
            List<String> rowData = new ArrayList<>();
            for (XWPFTableCell cell : row.getTableCells()) {
                rowData.add(cell.getText().trim());
            }
            tableData.add(rowData);
        }
        
        return ContentBlock.builder()
            .type(BlockType.TABLE)
            .content(formatTableAsText(tableData))
            .structuredData(tableData)
            .build();
    }
}
```

#### 1.3 飞书文档处理
```java
@Component
public class FeishuDocumentProcessor implements DocumentProcessor {

    @Autowired
    private FeishuApiClient feishuApiClient;

    @Autowired
    private FeishuWebhookService webhookService;

    public DocumentContent process(InputStream inputStream, ProcessConfig config) {
        FeishuDocumentRequest request = parseRequest(inputStream);

        // API认证
        String accessToken = feishuApiClient.getAccessToken();

        // 获取文档信息
        FeishuDocumentInfo docInfo = feishuApiClient.getDocumentInfo(
            request.getDocumentId(), accessToken);

        // 根据文档类型选择处理策略
        switch (docInfo.getDocumentType()) {
            case FEISHU_DOC:
                return processFeishuDoc(docInfo, accessToken);
            case FEISHU_SHEET:
                return processFeishuSheet(docInfo, accessToken);
            case FEISHU_BITABLE:
                return processFeishuBitable(docInfo, accessToken);
            case FEISHU_MINDNOTE:
                return processFeishuMindnote(docInfo, accessToken);
            case FEISHU_WHITEBOARD:
                return processFeishuWhiteboard(docInfo, accessToken);
            default:
                throw new UnsupportedDocumentTypeException("不支持的飞书文档类型: " + docInfo.getDocumentType());
        }
    }

    private DocumentContent processFeishuDoc(FeishuDocumentInfo docInfo, String accessToken) {
        // 获取文档内容
        FeishuDocContent docContent = feishuApiClient.getDocumentContent(
            docInfo.getDocumentId(), accessToken);

        List<ContentBlock> blocks = new ArrayList<>();

        // 处理文档块
        for (FeishuDocBlock block : docContent.getBlocks()) {
            ContentBlock contentBlock = convertFeishuBlock(block);
            blocks.add(contentBlock);
        }

        // 获取协作信息
        List<FeishuComment> comments = feishuApiClient.getDocumentComments(
            docInfo.getDocumentId(), accessToken);

        // 获取权限信息
        FeishuPermissionInfo permissions = feishuApiClient.getDocumentPermissions(
            docInfo.getDocumentId(), accessToken);

        return DocumentContent.builder()
            .blocks(blocks)
            .documentType(DocumentType.FEISHU_DOC)
            .collaborationInfo(convertComments(comments))
            .permissionInfo(convertPermissions(permissions))
            .metadata(extractFeishuMetadata(docInfo))
            .build();
    }

    private DocumentContent processFeishuSheet(FeishuDocumentInfo docInfo, String accessToken) {
        // 获取表格信息
        FeishuSheetInfo sheetInfo = feishuApiClient.getSheetInfo(
            docInfo.getDocumentId(), accessToken);

        List<ContentBlock> blocks = new ArrayList<>();

        // 处理每个工作表
        for (FeishuWorksheet worksheet : sheetInfo.getWorksheets()) {
            // 获取工作表数据
            FeishuSheetData sheetData = feishuApiClient.getSheetData(
                docInfo.getDocumentId(), worksheet.getSheetId(), accessToken);

            ContentBlock sheetBlock = ContentBlock.builder()
                .type(BlockType.TABLE)
                .content(formatSheetAsText(sheetData))
                .structuredData(convertSheetData(sheetData))
                .metadata(Map.of(
                    "worksheet_name", worksheet.getTitle(),
                    "sheet_id", worksheet.getSheetId(),
                    "row_count", sheetData.getRowCount(),
                    "column_count", sheetData.getColumnCount()
                ))
                .build();
            blocks.add(sheetBlock);
        }

        return DocumentContent.builder()
            .blocks(blocks)
            .documentType(DocumentType.FEISHU_SHEET)
            .metadata(extractFeishuMetadata(docInfo))
            .build();
    }

    private DocumentContent processFeishuBitable(FeishuDocumentInfo docInfo, String accessToken) {
        // 获取多维表格信息
        FeishuBitableInfo bitableInfo = feishuApiClient.getBitableInfo(
            docInfo.getDocumentId(), accessToken);

        List<ContentBlock> blocks = new ArrayList<>();

        // 处理每个数据表
        for (FeishuDataTable dataTable : bitableInfo.getDataTables()) {
            // 获取字段定义
            List<FeishuField> fields = feishuApiClient.getBitableFields(
                docInfo.getDocumentId(), dataTable.getTableId(), accessToken);

            // 获取记录数据
            List<FeishuRecord> records = feishuApiClient.getBitableRecords(
                docInfo.getDocumentId(), dataTable.getTableId(), accessToken);

            ContentBlock tableBlock = ContentBlock.builder()
                .type(BlockType.STRUCTURED_TABLE)
                .content(formatBitableAsText(dataTable, fields, records))
                .structuredData(convertBitableData(fields, records))
                .metadata(Map.of(
                    "table_name", dataTable.getName(),
                    "table_id", dataTable.getTableId(),
                    "field_count", fields.size(),
                    "record_count", records.size()
                ))
                .build();
            blocks.add(tableBlock);
        }

        return DocumentContent.builder()
            .blocks(blocks)
            .documentType(DocumentType.FEISHU_BITABLE)
            .metadata(extractFeishuMetadata(docInfo))
            .build();
    }

    private ContentBlock convertFeishuBlock(FeishuDocBlock feishuBlock) {
        BlockType blockType = mapFeishuBlockType(feishuBlock.getBlockType());

        return ContentBlock.builder()
            .type(blockType)
            .content(extractTextFromFeishuBlock(feishuBlock))
            .style(convertFeishuStyle(feishuBlock.getStyle()))
            .metadata(Map.of(
                "feishu_block_id", feishuBlock.getBlockId(),
                "feishu_block_type", feishuBlock.getBlockType(),
                "parent_id", feishuBlock.getParentId()
            ))
            .build();
    }
}
```

### 2. 飞书API集成服务

#### 2.1 飞书API客户端
```java
@Service
public class FeishuApiClient {

    @Value("${feishu.app.id}")
    private String appId;

    @Value("${feishu.app.secret}")
    private String appSecret;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String FEISHU_API_BASE_URL = "https://open.feishu.cn/open-apis";
    private static final String ACCESS_TOKEN_CACHE_KEY = "feishu:access_token";

    public String getAccessToken() {
        // 从缓存获取访问令牌
        String cachedToken = redisTemplate.opsForValue().get(ACCESS_TOKEN_CACHE_KEY);
        if (StringUtils.hasText(cachedToken)) {
            return cachedToken;
        }

        // 获取新的访问令牌
        String url = FEISHU_API_BASE_URL + "/auth/v3/tenant_access_token/internal";

        Map<String, String> requestBody = Map.of(
            "app_id", appId,
            "app_secret", appSecret
        );

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<FeishuTokenResponse> response = restTemplate.postForEntity(
                url, request, FeishuTokenResponse.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                FeishuTokenResponse tokenResponse = response.getBody();
                if (tokenResponse.getCode() == 0) {
                    String accessToken = tokenResponse.getTenantAccessToken();

                    // 缓存访问令牌（设置过期时间为1小时50分钟，提前10分钟刷新）
                    redisTemplate.opsForValue().set(ACCESS_TOKEN_CACHE_KEY, accessToken,
                        Duration.ofMinutes(110));

                    return accessToken;
                }
            }
        } catch (Exception e) {
            log.error("获取飞书访问令牌失败", e);
        }

        throw new FeishuApiException("无法获取飞书访问令牌");
    }

    public FeishuDocumentInfo getDocumentInfo(String documentId, String accessToken) {
        String url = FEISHU_API_BASE_URL + "/drive/v1/files/" + documentId + "/meta";

        HttpHeaders headers = createAuthHeaders(accessToken);
        HttpEntity<Void> request = new HttpEntity<>(headers);

        try {
            ResponseEntity<FeishuDocumentInfoResponse> response = restTemplate.exchange(
                url, HttpMethod.GET, request, FeishuDocumentInfoResponse.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody().getData();
            }
        } catch (Exception e) {
            log.error("获取飞书文档信息失败: documentId={}", documentId, e);
        }

        throw new FeishuApiException("无法获取文档信息: " + documentId);
    }

    public FeishuDocContent getDocumentContent(String documentId, String accessToken) {
        String url = FEISHU_API_BASE_URL + "/docx/v1/documents/" + documentId + "/blocks";

        HttpHeaders headers = createAuthHeaders(accessToken);
        HttpEntity<Void> request = new HttpEntity<>(headers);

        try {
            ResponseEntity<FeishuDocContentResponse> response = restTemplate.exchange(
                url, HttpMethod.GET, request, FeishuDocContentResponse.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody().getData();
            }
        } catch (Exception e) {
            log.error("获取飞书文档内容失败: documentId={}", documentId, e);
        }

        throw new FeishuApiException("无法获取文档内容: " + documentId);
    }

    public List<FeishuComment> getDocumentComments(String documentId, String accessToken) {
        String url = FEISHU_API_BASE_URL + "/drive/v1/files/" + documentId + "/comments";

        HttpHeaders headers = createAuthHeaders(accessToken);
        HttpEntity<Void> request = new HttpEntity<>(headers);

        try {
            ResponseEntity<FeishuCommentsResponse> response = restTemplate.exchange(
                url, HttpMethod.GET, request, FeishuCommentsResponse.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody().getData().getItems();
            }
        } catch (Exception e) {
            log.error("获取飞书文档评论失败: documentId={}", documentId, e);
        }

        return Collections.emptyList();
    }

    private HttpHeaders createAuthHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(accessToken);
        return headers;
    }
}
```

#### 2.2 飞书Webhook事件监听服务
```java
@Service
public class FeishuWebhookService {

    @Autowired
    private DocumentExtractionService documentExtractionService;

    @Autowired
    private FeishuApiClient feishuApiClient;

    @Value("${feishu.webhook.verification.token}")
    private String verificationToken;

    @Value("${feishu.webhook.encrypt.key}")
    private String encryptKey;

    @EventListener
    public void handleFeishuDocumentEvent(FeishuDocumentEvent event) {
        try {
            // 验证事件来源
            if (!verifyEventSignature(event)) {
                log.warn("飞书事件签名验证失败: {}", event);
                return;
            }

            // 处理不同类型的文档事件
            switch (event.getEventType()) {
                case "drive.file.title_updated_v1":
                    handleDocumentTitleUpdated(event);
                    break;
                case "drive.file.edit_v1":
                    handleDocumentContentUpdated(event);
                    break;
                case "drive.file.collaborator_added_v1":
                    handleCollaboratorAdded(event);
                    break;
                case "drive.file.collaborator_removed_v1":
                    handleCollaboratorRemoved(event);
                    break;
                case "drive.file.trashed_v1":
                    handleDocumentTrashed(event);
                    break;
                default:
                    log.info("未处理的飞书事件类型: {}", event.getEventType());
            }
        } catch (Exception e) {
            log.error("处理飞书文档事件失败", e);
        }
    }

    private void handleDocumentContentUpdated(FeishuDocumentEvent event) {
        String documentId = event.getDocumentId();

        // 增量同步文档内容
        try {
            // 重新提取文档内容
            DocumentExtractionRequest request = DocumentExtractionRequest.builder()
                .documentId(documentId)
                .documentType(DocumentType.FEISHU_DOC)
                .syncMode(SyncMode.INCREMENTAL)
                .build();

            documentExtractionService.processDocument(request);

            log.info("飞书文档增量同步完成: documentId={}", documentId);
        } catch (Exception e) {
            log.error("飞书文档增量同步失败: documentId={}", documentId, e);
        }
    }

    private boolean verifyEventSignature(FeishuDocumentEvent event) {
        // 实现飞书事件签名验证逻辑
        String expectedSignature = calculateEventSignature(event);
        return expectedSignature.equals(event.getSignature());
    }

    private String calculateEventSignature(FeishuDocumentEvent event) {
        // 使用HMAC-SHA256计算事件签名
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(encryptKey.getBytes(), "HmacSHA256");
            mac.init(secretKey);

            String data = event.getTimestamp() + event.getNonce() + event.getBody();
            byte[] signature = mac.doFinal(data.getBytes());

            return Base64.getEncoder().encodeToString(signature);
        } catch (Exception e) {
            log.error("计算飞书事件签名失败", e);
            return "";
        }
    }
}
```

#### 2.3 图片文档处理
```java
@Component
public class ImageProcessor implements DocumentProcessor {
    
    @Autowired
    private HeheCombOcrClient ocrClient;
    
    public DocumentContent process(InputStream inputStream, ProcessConfig config) {
        BufferedImage image = ImageIO.read(inputStream);
        
        // 图像预处理
        BufferedImage processedImage = preprocessImage(image);
        
        // OCR识别
        OcrResult ocrResult = ocrClient.recognizeDocument(processedImage);
        
        // 版面分析
        LayoutAnalysisResult layout = ocrClient.analyzeLayout(processedImage);
        
        List<ContentBlock> blocks = new ArrayList<>();
        
        // 按版面区域提取内容
        for (LayoutRegion region : layout.getRegions()) {
            ContentBlock block = ContentBlock.builder()
                .type(mapRegionType(region.getType()))
                .content(region.getText())
                .boundingBox(region.getBoundingBox())
                .confidence(region.getConfidence())
                .build();
            blocks.add(block);
        }
        
        return DocumentContent.builder()
            .blocks(blocks)
            .documentType(DocumentType.IMAGE)
            .layoutInfo(layout)
            .build();
    }
    
    private BufferedImage preprocessImage(BufferedImage image) {
        // 图像增强处理
        // 1. 去噪
        // 2. 对比度调整
        // 3. 倾斜校正
        return ImageEnhancer.enhance(image);
    }
}
```

### 3. 飞书文档同步调度服务

#### 3.1 定时同步任务
```java
@Component
public class FeishuDocumentSyncScheduler {

    @Autowired
    private FeishuApiClient feishuApiClient;

    @Autowired
    private DocumentExtractionService documentExtractionService;

    @Autowired
    private FeishuDocumentRepository documentRepository;

    // 每小时执行一次增量同步
    @Scheduled(cron = "0 0 * * * ?")
    public void performIncrementalSync() {
        log.info("开始执行飞书文档增量同步任务");

        try {
            // 获取需要同步的文档列表
            List<FeishuDocumentRecord> documentsToSync = documentRepository.findDocumentsForSync();

            for (FeishuDocumentRecord record : documentsToSync) {
                try {
                    syncSingleDocument(record);
                } catch (Exception e) {
                    log.error("同步单个文档失败: documentId={}", record.getDocumentId(), e);
                }
            }

            log.info("飞书文档增量同步任务完成，处理文档数量: {}", documentsToSync.size());
        } catch (Exception e) {
            log.error("飞书文档增量同步任务执行失败", e);
        }
    }

    // 每天凌晨2点执行全量同步
    @Scheduled(cron = "0 0 2 * * ?")
    public void performFullSync() {
        log.info("开始执行飞书文档全量同步任务");

        try {
            String accessToken = feishuApiClient.getAccessToken();

            // 获取所有有权限访问的文档
            List<FeishuDocumentInfo> allDocuments = feishuApiClient.getAllAccessibleDocuments(accessToken);

            for (FeishuDocumentInfo docInfo : allDocuments) {
                try {
                    performFullDocumentSync(docInfo);
                } catch (Exception e) {
                    log.error("全量同步单个文档失败: documentId={}", docInfo.getDocumentId(), e);
                }
            }

            log.info("飞书文档全量同步任务完成，处理文档数量: {}", allDocuments.size());
        } catch (Exception e) {
            log.error("飞书文档全量同步任务执行失败", e);
        }
    }

    private void syncSingleDocument(FeishuDocumentRecord record) {
        String accessToken = feishuApiClient.getAccessToken();

        // 检查文档是否有更新
        FeishuDocumentInfo currentInfo = feishuApiClient.getDocumentInfo(
            record.getDocumentId(), accessToken);

        if (currentInfo.getLastModifiedTime().isAfter(record.getLastSyncTime())) {
            // 文档有更新，执行同步
            DocumentExtractionRequest request = DocumentExtractionRequest.builder()
                .documentId(record.getDocumentId())
                .documentType(mapFeishuDocumentType(currentInfo.getType()))
                .syncMode(SyncMode.INCREMENTAL)
                .lastSyncTime(record.getLastSyncTime())
                .build();

            documentExtractionService.processDocument(request);

            // 更新同步记录
            record.setLastSyncTime(Instant.now());
            documentRepository.save(record);
        }
    }

    private void performFullDocumentSync(FeishuDocumentInfo docInfo) {
        DocumentExtractionRequest request = DocumentExtractionRequest.builder()
            .documentId(docInfo.getDocumentId())
            .documentType(mapFeishuDocumentType(docInfo.getType()))
            .syncMode(SyncMode.FULL)
            .build();

        documentExtractionService.processDocument(request);

        // 创建或更新同步记录
        FeishuDocumentRecord record = documentRepository.findByDocumentId(docInfo.getDocumentId())
            .orElse(new FeishuDocumentRecord());

        record.setDocumentId(docInfo.getDocumentId());
        record.setDocumentTitle(docInfo.getTitle());
        record.setDocumentType(docInfo.getType());
        record.setLastSyncTime(Instant.now());

        documentRepository.save(record);
    }
}
```

#### 3.2 飞书文档数据模型
```java
@Entity
@Table(name = "feishu_document_sync_record")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeishuDocumentRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "document_id", unique = true, nullable = false)
    private String documentId;

    @Column(name = "document_title")
    private String documentTitle;

    @Column(name = "document_type")
    private String documentType;

    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

    @Column(name = "sync_status")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "created_time")
    private Instant createdTime;

    @Column(name = "updated_time")
    private Instant updatedTime;

    @PrePersist
    protected void onCreate() {
        createdTime = Instant.now();
        updatedTime = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = Instant.now();
    }
}

public enum SyncStatus {
    PENDING,    // 待同步
    SYNCING,    // 同步中
    SUCCESS,    // 同步成功
    FAILED      // 同步失败
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeishuDocumentEvent {
    private String eventType;
    private String documentId;
    private String timestamp;
    private String nonce;
    private String signature;
    private String body;
    private Map<String, Object> eventData;
}
```

### 4. 内容预处理管道

#### 4.1 预处理流水线设计
```java
@Component
public class DocumentPreprocessingPipeline {
    
    private final List<PreprocessingStage> stages;
    
    public DocumentPreprocessingPipeline() {
        this.stages = Arrays.asList(
            new FormatDetectionStage(),
            new ContentExtractionStage(),
            new NoiseFilteringStage(),
            new StructureAnalysisStage(),
            new MetadataEnrichmentStage()
        );
    }
    
    public ProcessedDocument process(RawDocument rawDocument) {
        ProcessingContext context = new ProcessingContext(rawDocument);
        
        for (PreprocessingStage stage : stages) {
            try {
                context = stage.process(context);
                logStageCompletion(stage, context);
            } catch (Exception e) {
                handleStageError(stage, context, e);
            }
        }
        
        return context.getProcessedDocument();
    }
}

public interface PreprocessingStage {
    ProcessingContext process(ProcessingContext context) throws ProcessingException;
    String getStageName();
    boolean isRequired();
}
```

#### 2.2 格式检测阶段
```java
@Component
public class FormatDetectionStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        RawDocument document = context.getRawDocument();
        
        DocumentFormat format = detectFormat(document);
        DocumentProcessor processor = getProcessor(format);
        
        context.setDocumentFormat(format);
        context.setProcessor(processor);
        
        return context;
    }
    
    private DocumentFormat detectFormat(RawDocument document) {
        String filename = document.getFilename();
        byte[] content = document.getContent();
        
        // 基于文件扩展名
        DocumentFormat formatByExtension = detectByExtension(filename);
        
        // 基于文件头魔数
        DocumentFormat formatByMagicNumber = detectByMagicNumber(content);
        
        // 基于内容特征
        DocumentFormat formatByContent = detectByContentAnalysis(content);
        
        // 综合判断
        return resolveFormatConflict(formatByExtension, formatByMagicNumber, formatByContent);
    }
}
```

#### 2.3 噪声过滤阶段
```java
@Component
public class NoiseFilteringStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        DocumentContent content = context.getDocumentContent();
        
        DocumentContent filteredContent = DocumentContent.builder()
            .blocks(filterBlocks(content.getBlocks()))
            .metadata(content.getMetadata())
            .build();
        
        context.setDocumentContent(filteredContent);
        return context;
    }
    
    private List<ContentBlock> filterBlocks(List<ContentBlock> blocks) {
        return blocks.stream()
            .filter(this::isValidBlock)
            .map(this::cleanBlock)
            .collect(Collectors.toList());
    }
    
    private boolean isValidBlock(ContentBlock block) {
        String content = block.getContent();
        
        // 过滤空内容
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        // 过滤页眉页脚
        if (isHeaderOrFooter(block)) {
            return false;
        }
        
        // 过滤水印
        if (isWatermark(block)) {
            return false;
        }
        
        // 过滤重复内容
        if (isDuplicate(block)) {
            return false;
        }
        
        return true;
    }
    
    private ContentBlock cleanBlock(ContentBlock block) {
        String content = block.getContent();
        
        // 清理特殊字符
        content = cleanSpecialCharacters(content);
        
        // 标准化空白字符
        content = normalizeWhitespace(content);
        
        // 修复编码问题
        content = fixEncoding(content);
        
        return block.toBuilder()
            .content(content)
            .build();
    }
}
```

### 3. 结构化信息提取

#### 3.1 文档结构分析
```java
@Component
public class StructureAnalysisStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        DocumentContent content = context.getDocumentContent();
        
        DocumentStructure structure = analyzeStructure(content);
        
        context.setDocumentStructure(structure);
        return context;
    }
    
    private DocumentStructure analyzeStructure(DocumentContent content) {
        List<ContentBlock> blocks = content.getBlocks();
        
        // 标题层级识别
        List<Heading> headings = extractHeadings(blocks);
        
        // 段落分组
        List<Section> sections = groupIntoSections(blocks, headings);
        
        // 表格识别
        List<Table> tables = extractTables(blocks);
        
        // 列表识别
        List<ListStructure> lists = extractLists(blocks);
        
        return DocumentStructure.builder()
            .headings(headings)
            .sections(sections)
            .tables(tables)
            .lists(lists)
            .build();
    }
    
    private List<Heading> extractHeadings(List<ContentBlock> blocks) {
        List<Heading> headings = new ArrayList<>();
        
        for (ContentBlock block : blocks) {
            if (isHeading(block)) {
                int level = determineHeadingLevel(block);
                Heading heading = Heading.builder()
                    .text(block.getContent())
                    .level(level)
                    .blockIndex(blocks.indexOf(block))
                    .build();
                headings.add(heading);
            }
        }
        
        return headings;
    }
    
    private boolean isHeading(ContentBlock block) {
        String content = block.getContent();
        StyleInfo style = block.getStyle();
        
        // 基于样式判断
        if (style != null && style.isBold() && style.getFontSize() > 12) {
            return true;
        }
        
        // 基于格式模式判断
        if (content.matches("^\\d+\\.\\s+.+") || content.matches("^[A-Z][^.]*$")) {
            return true;
        }
        
        // 基于长度判断
        if (content.length() < 100 && !content.endsWith(".")) {
            return true;
        }
        
        return false;
    }
}
```

#### 3.2 元数据增强
```java
@Component
public class MetadataEnrichmentStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        DocumentContent content = context.getDocumentContent();
        DocumentStructure structure = context.getDocumentStructure();
        
        EnrichedMetadata metadata = enrichMetadata(content, structure);
        
        context.setEnrichedMetadata(metadata);
        return context;
    }
    
    private EnrichedMetadata enrichMetadata(DocumentContent content, DocumentStructure structure) {
        return EnrichedMetadata.builder()
            .documentSummary(generateSummary(content))
            .keyPhrases(extractKeyPhrases(content))
            .entities(extractEntities(content))
            .topics(identifyTopics(content))
            .language(detectLanguage(content))
            .readabilityScore(calculateReadability(content))
            .structureComplexity(analyzeComplexity(structure))
            .build();
    }
    
    private List<String> extractKeyPhrases(DocumentContent content) {
        String fullText = content.getBlocks().stream()
            .map(ContentBlock::getContent)
            .collect(Collectors.joining(" "));

        // 使用阿里云NLP提取关键词
        return aliCloudNlpClient.extractKeyPhrases(fullText);
    }
}
```

### 5. 飞书文档知识图谱增强

#### 5.1 飞书协作信息提取
```java
@Service
public class FeishuCollaborationKnowledgeService {

    @Autowired
    private FeishuApiClient feishuApiClient;

    @Autowired
    private EntityRecognitionService entityRecognitionService;

    public CollaborationKnowledgeData extractCollaborationKnowledge(String documentId, String accessToken) {
        // 1. 提取协作者信息
        List<FeishuCollaborator> collaborators = feishuApiClient.getDocumentCollaborators(documentId, accessToken);

        // 2. 提取评论和批注
        List<FeishuComment> comments = feishuApiClient.getDocumentComments(documentId, accessToken);

        // 3. 提取修改历史
        List<FeishuRevision> revisions = feishuApiClient.getDocumentRevisions(documentId, accessToken);

        // 4. 构建协作知识图谱
        return buildCollaborationKnowledgeGraph(collaborators, comments, revisions);
    }

    private CollaborationKnowledgeData buildCollaborationKnowledgeGraph(
            List<FeishuCollaborator> collaborators,
            List<FeishuComment> comments,
            List<FeishuRevision> revisions) {

        List<CollaborationEntity> entities = new ArrayList<>();
        List<CollaborationRelationship> relationships = new ArrayList<>();

        // 处理协作者实体
        for (FeishuCollaborator collaborator : collaborators) {
            CollaborationEntity entity = CollaborationEntity.builder()
                .entityId(collaborator.getUserId())
                .entityType(EntityType.PERSON)
                .name(collaborator.getName())
                .email(collaborator.getEmail())
                .role(collaborator.getRole())
                .build();
            entities.add(entity);
        }

        // 处理评论关系
        for (FeishuComment comment : comments) {
            // 创建评论实体
            CollaborationEntity commentEntity = CollaborationEntity.builder()
                .entityId(comment.getCommentId())
                .entityType(EntityType.COMMENT)
                .content(comment.getContent())
                .createTime(comment.getCreateTime())
                .build();
            entities.add(commentEntity);

            // 创建用户-评论关系
            CollaborationRelationship relationship = CollaborationRelationship.builder()
                .subjectId(comment.getUserId())
                .predicateType(RelationshipType.COMMENTED_ON)
                .objectId(comment.getCommentId())
                .timestamp(comment.getCreateTime())
                .build();
            relationships.add(relationship);

            // 处理评论回复关系
            if (comment.getParentCommentId() != null) {
                CollaborationRelationship replyRelationship = CollaborationRelationship.builder()
                    .subjectId(comment.getCommentId())
                    .predicateType(RelationshipType.REPLIES_TO)
                    .objectId(comment.getParentCommentId())
                    .timestamp(comment.getCreateTime())
                    .build();
                relationships.add(replyRelationship);
            }
        }

        return CollaborationKnowledgeData.builder()
            .entities(entities)
            .relationships(relationships)
            .build();
    }
}
```

#### 5.2 飞书文档语义增强
```java
@Service
public class FeishuDocumentSemanticEnhancer {

    @Autowired
    private EntityRecognitionService entityRecognitionService;

    @Autowired
    private RelationshipExtractionService relationshipExtractionService;

    public SemanticEnhancedDocument enhanceFeishuDocument(DocumentContent documentContent) {
        List<SemanticBlock> enhancedBlocks = new ArrayList<>();

        for (ContentBlock block : documentContent.getBlocks()) {
            SemanticBlock semanticBlock = enhanceBlock(block);
            enhancedBlocks.add(semanticBlock);
        }

        // 跨块实体关系分析
        List<CrossBlockRelationship> crossBlockRelationships = analyzeCrossBlockRelationships(enhancedBlocks);

        return SemanticEnhancedDocument.builder()
            .originalDocument(documentContent)
            .semanticBlocks(enhancedBlocks)
            .crossBlockRelationships(crossBlockRelationships)
            .documentSemanticSummary(generateDocumentSemanticSummary(enhancedBlocks))
            .build();
    }

    private SemanticBlock enhanceBlock(ContentBlock block) {
        // 实体识别
        List<RecognizedEntity> entities = entityRecognitionService.recognize(
            block.getContent(), EntityRecognitionConfig.forFeishuDocument());

        // 关系抽取
        List<ExtractedRelationship> relationships = relationshipExtractionService.extract(
            block.getContent(), entities, RelationshipExtractionConfig.forFeishuDocument());

        // 语义标签
        List<SemanticTag> semanticTags = generateSemanticTags(block, entities, relationships);

        return SemanticBlock.builder()
            .originalBlock(block)
            .entities(entities)
            .relationships(relationships)
            .semanticTags(semanticTags)
            .semanticScore(calculateSemanticScore(entities, relationships))
            .build();
    }

    private List<SemanticTag> generateSemanticTags(ContentBlock block,
                                                  List<RecognizedEntity> entities,
                                                  List<ExtractedRelationship> relationships) {
        List<SemanticTag> tags = new ArrayList<>();

        // 基于实体类型生成标签
        Map<String, Long> entityTypeCounts = entities.stream()
            .collect(Collectors.groupingBy(RecognizedEntity::getEntityType, Collectors.counting()));

        for (Map.Entry<String, Long> entry : entityTypeCounts.entrySet()) {
            if (entry.getValue() >= 2) { // 实体类型出现2次以上才生成标签
                tags.add(SemanticTag.builder()
                    .tagType(TagType.ENTITY_FOCUS)
                    .tagValue(entry.getKey())
                    .confidence(calculateEntityFocusConfidence(entry.getValue(), entities.size()))
                    .build());
            }
        }

        // 基于关系类型生成标签
        Map<String, Long> relationTypeCounts = relationships.stream()
            .collect(Collectors.groupingBy(ExtractedRelationship::getRelationType, Collectors.counting()));

        for (Map.Entry<String, Long> entry : relationTypeCounts.entrySet()) {
            tags.add(SemanticTag.builder()
                .tagType(TagType.RELATION_PATTERN)
                .tagValue(entry.getKey())
                .confidence(calculateRelationPatternConfidence(entry.getValue(), relationships.size()))
                .build());
        }

        return tags;
    }
}
```

### 6. 知识图谱数据准备服务

#### 6.1 实体识别与关系抽取服务
```java
@Service
public class KnowledgeGraphDataPreparationService {

    @Autowired
    private EntityRecognitionService entityRecognitionService;

    @Autowired
    private RelationshipExtractionService relationshipExtractionService;

    @Autowired
    private EntityLinkingService entityLinkingService;

    public KnowledgeGraphData prepareKnowledgeGraphData(DocumentContent content, KGDataConfig config) {
        // 1. 实体识别
        List<RecognizedEntity> entities = recognizeEntities(content, config.getEntityConfig());

        // 2. 关系抽取
        List<ExtractedRelationship> relationships = extractRelationships(content, entities, config.getRelationshipConfig());

        // 3. 实体链接
        List<LinkedEntity> linkedEntities = linkEntities(entities, config.getLinkingConfig());

        // 4. 生成知识图谱三元组
        List<KnowledgeTriple> triples = generateTriples(linkedEntities, relationships, config.getTripleConfig());

        return KnowledgeGraphData.builder()
            .entities(linkedEntities)
            .relationships(relationships)
            .triples(triples)
            .build();
    }

    private List<RecognizedEntity> recognizeEntities(DocumentContent content, EntityRecognitionConfig config) {
        List<RecognizedEntity> allEntities = new ArrayList<>();

        for (ContentBlock block : content.getBlocks()) {
            // 使用NER模型识别实体
            List<RecognizedEntity> blockEntities = entityRecognitionService.recognize(
                block.getContent(), config
            );

            // 添加位置信息
            for (RecognizedEntity entity : blockEntities) {
                entity.setBlockId(block.getId());
                entity.setDocumentId(content.getDocumentId());
            }

            allEntities.addAll(blockEntities);
        }

        // 实体去重和合并
        return mergeAndDeduplicateEntities(allEntities, config);
    }

    private List<ExtractedRelationship> extractRelationships(DocumentContent content,
                                                           List<RecognizedEntity> entities,
                                                           RelationshipExtractionConfig config) {
        List<ExtractedRelationship> relationships = new ArrayList<>();

        for (ContentBlock block : content.getBlocks()) {
            // 获取该块中的实体
            List<RecognizedEntity> blockEntities = entities.stream()
                .filter(e -> e.getBlockId().equals(block.getId()))
                .collect(Collectors.toList());

            if (blockEntities.size() >= 2) {
                // 抽取实体间的关系
                List<ExtractedRelationship> blockRelationships = relationshipExtractionService.extract(
                    block.getContent(), blockEntities, config
                );
                relationships.addAll(blockRelationships);
            }
        }

        return relationships;
    }

    private List<LinkedEntity> linkEntities(List<RecognizedEntity> entities, EntityLinkingConfig config) {
        List<LinkedEntity> linkedEntities = new ArrayList<>();

        for (RecognizedEntity entity : entities) {
            // 实体链接到知识库
            LinkedEntity linked = entityLinkingService.link(entity, config);
            if (linked != null) {
                linkedEntities.add(linked);
            }
        }

        return linkedEntities;
    }

    private List<KnowledgeTriple> generateTriples(List<LinkedEntity> entities,
                                                List<ExtractedRelationship> relationships,
                                                TripleGenerationConfig config) {
        List<KnowledgeTriple> triples = new ArrayList<>();

        for (ExtractedRelationship relationship : relationships) {
            // 查找关系的主体和客体实体
            LinkedEntity subject = findEntityById(entities, relationship.getSubjectId());
            LinkedEntity object = findEntityById(entities, relationship.getObjectId());

            if (subject != null && object != null) {
                KnowledgeTriple triple = KnowledgeTriple.builder()
                    .subject(subject.getKnowledgeBaseId())
                    .predicate(relationship.getRelationType())
                    .object(object.getKnowledgeBaseId())
                    .confidence(relationship.getConfidence())
                    .source(relationship.getSource())
                    .build();

                triples.add(triple);
            }
        }

        return triples;
    }
    
    private List<Entity> extractEntities(DocumentContent content) {
        String fullText = content.getBlocks().stream()
            .map(ContentBlock::getContent)
            .collect(Collectors.joining(" "));
        
        // 使用阿里云NLP进行实体识别
        return aliCloudNlpClient.recognizeEntities(fullText);
    }
}
```

## 配置与扩展

### 飞书集成配置
```yaml
# application.yml
feishu:
  app:
    id: ${FEISHU_APP_ID}
    secret: ${FEISHU_APP_SECRET}
  webhook:
    verification:
      token: ${FEISHU_WEBHOOK_VERIFICATION_TOKEN}
    encrypt:
      key: ${FEISHU_WEBHOOK_ENCRYPT_KEY}
  api:
    base-url: https://open.feishu.cn/open-apis
    timeout: 30000
    retry-count: 3
  sync:
    enabled: true
    incremental-cron: "0 0 * * * ?"  # 每小时执行增量同步
    full-sync-cron: "0 0 2 * * ?"    # 每天凌晨2点执行全量同步
    batch-size: 100
    max-concurrent-tasks: 5
```

```java
@ConfigurationProperties(prefix = "feishu")
@Data
public class FeishuConfig {

    private App app = new App();
    private Webhook webhook = new Webhook();
    private Api api = new Api();
    private Sync sync = new Sync();

    @Data
    public static class App {
        private String id;
        private String secret;
    }

    @Data
    public static class Webhook {
        private Verification verification = new Verification();
        private Encrypt encrypt = new Encrypt();

        @Data
        public static class Verification {
            private String token;
        }

        @Data
        public static class Encrypt {
            private String key;
        }
    }

    @Data
    public static class Api {
        private String baseUrl = "https://open.feishu.cn/open-apis";
        private int timeout = 30000;
        private int retryCount = 3;
    }

    @Data
    public static class Sync {
        private boolean enabled = true;
        private String incrementalCron = "0 0 * * * ?";
        private String fullSyncCron = "0 0 2 * * ?";
        private int batchSize = 100;
        private int maxConcurrentTasks = 5;
    }
}
```

### 处理配置
```java
@ConfigurationProperties(prefix = "rag.document.extraction")
@Data
public class ExtractionConfig {
    
    // OCR配置
    private OcrConfig ocr = new OcrConfig();

    // 飞书配置
    private FeishuConfig feishu = new FeishuConfig();

    // 预处理配置
    private PreprocessingConfig preprocessing = new PreprocessingConfig();

    // 格式支持配置
    private Map<String, Boolean> supportedFormats = new HashMap<>();
    
    @Data
    public static class OcrConfig {
        private String apiKey;
        private String endpoint;
        private int timeout = 30000;
        private int retryCount = 3;
        private double confidenceThreshold = 0.8;
    }
    
    @Data
    public static class PreprocessingConfig {
        private boolean enableNoiseFiltering = true;
        private boolean enableStructureAnalysis = true;
        private boolean enableMetadataEnrichment = true;
        private int maxContentLength = 1000000;
        private List<String> excludePatterns = new ArrayList<>();
    }
}
```

### 扩展接口
```java
public interface DocumentProcessor {
    DocumentContent process(InputStream inputStream, ProcessConfig config);
    boolean supports(DocumentFormat format);
    int getPriority();
}

public interface ContentEnricher {
    EnrichedContent enrich(DocumentContent content);
    String getEnricherName();
}

// 飞书文档处理器接口
public interface FeishuDocumentProcessor {
    DocumentContent processFeishuDocument(String documentId, FeishuDocumentType documentType, String accessToken);
    boolean supportsFeishuDocumentType(FeishuDocumentType documentType);
    FeishuSyncResult syncDocument(String documentId, SyncMode syncMode);
}

// 飞书事件处理器接口
public interface FeishuEventHandler {
    void handleEvent(FeishuDocumentEvent event);
    boolean supportsEventType(String eventType);
    int getPriority();
}

// 协作知识提取器接口
public interface CollaborationKnowledgeExtractor {
    CollaborationKnowledgeData extractKnowledge(String documentId, String accessToken);
    List<CollaborationEntity> extractCollaborators(String documentId, String accessToken);
    List<CollaborationRelationship> extractCollaborationRelationships(String documentId, String accessToken);
}
```

### 飞书文档类型枚举
```java
public enum FeishuDocumentType {
    FEISHU_DOC("doc", "飞书文档"),
    FEISHU_SHEET("sheet", "飞书表格"),
    FEISHU_BITABLE("bitable", "飞书多维表格"),
    FEISHU_MINDNOTE("mindnote", "飞书思维导图"),
    FEISHU_WHITEBOARD("whiteboard", "飞书白板"),
    FEISHU_SLIDES("slides", "飞书演示文稿");

    private final String code;
    private final String description;

    FeishuDocumentType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static FeishuDocumentType fromCode(String code) {
        for (FeishuDocumentType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("不支持的飞书文档类型: " + code);
    }
}

public enum SyncMode {
    FULL,        // 全量同步
    INCREMENTAL  // 增量同步
}
```
