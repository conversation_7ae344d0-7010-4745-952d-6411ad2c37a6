# Text2SQL检索模块 (Text-to-SQL Retrieval)

## 模块概述

Text2SQL检索模块是RAG系统中专门处理结构化数据查询的核心组件，负责将自然语言查询转换为SQL语句，并对结构化数据（表格数据、数据库表等）进行智能查询。该模块与传统向量检索形成互补，专门解决传统向量检索无法有效处理的结构化数据场景。

## 核心功能

### 1. 自然语言到SQL转换
- **查询解析**：理解自然语言查询的语义和意图
- **SQL生成**：将自然语言转换为标准SQL语句
- **语法验证**：确保生成的SQL语句语法正确
- **优化建议**：提供SQL性能优化建议

### 2. 结构化数据Schema理解
- **表结构分析**：自动分析表结构、字段类型、约束关系
- **语义映射**：建立自然语言概念与数据库字段的映射关系
- **关系推理**：理解表间关系和外键约束
- **数据类型推断**：智能推断查询条件的数据类型

### 3. 多数据源支持
- **结构化数据**：关系数据库（MySQL、PostgreSQL、SQLite）
- **半结构化数据**：JSON文档、XML文件、CSV/Excel表格
- **NoSQL数据库**：MongoDB、Elasticsearch、Redis
- **文件数据源**：本地文件、云存储文件、API数据源

## 技术架构

### 整体架构图

```mermaid
graph TD
    A[用户自然语言提问] --> B[查询预处理与意图识别]
    B --> C[表结构向量检索]
    C --> D[最相关表结构提取]
    D --> E[Schema信息向量化]
    E --> F[LLM生成SQL语句]
    F --> G[SQL验证与优化]
    G --> H[执行SQL查询]
    H --> I[查询结果获取]
    I --> J[LLM生成自然语言回答]
    J --> K[最终回答输出]

    subgraph "表结构管理"
        L[数据源扫描] --> M[表结构提取]
        M --> N[Schema向量化]
        N --> O[向量数据库存储]
        O --> C
    end

    subgraph "SpringAI集成"
        P[ChatClient配置] --> F
        P --> J
        Q[Embedding模型] --> E
        Q --> C
    end

    subgraph "数据源支持"
        R[关系数据库<br/>MySQL/PostgreSQL] --> H
        S[NoSQL数据库<br/>MongoDB/Elasticsearch] --> H
        T[半结构化数据<br/>JSON/XML/CSV] --> H
    end

    subgraph "缓存与优化"
        U[Schema缓存] --> D
        V[SQL缓存] --> G
        W[结果缓存] --> I
    end

    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style P fill:#fff3e0
    style Q fill:#fff3e0
```

### 详细流程说明

#### 1. 用户提问阶段
- **输入**：用户的自然语言查询（如："查询销售额最高的前10个产品"）
- **处理**：查询清洗、意图识别、实体提取
- **输出**：结构化的查询对象

#### 2. 表结构向量检索阶段
- **输入**：处理后的查询对象
- **处理**：将查询向量化，在表结构向量库中检索最相关的表
- **输出**：相关度排序的表结构列表

#### 3. Schema信息向量化阶段
- **输入**：最相关的表结构信息
- **处理**：将表结构、字段信息、约束关系等转换为向量表示
- **输出**：增强的上下文信息

#### 4. LLM生成SQL阶段
- **输入**：查询意图 + 表结构信息 + 示例SQL
- **处理**：使用SpringAI的ChatClient生成SQL语句
- **输出**：标准SQL语句

#### 5. SQL执行阶段
- **输入**：验证后的SQL语句
- **处理**：在对应数据源上执行查询
- **输出**：结构化查询结果

#### 6. 自然语言生成阶段
- **输入**：SQL执行结果 + 原始查询
- **处理**：使用LLM将结构化结果转换为自然语言
- **输出**：用户友好的自然语言回答

### 核心组件

#### 1. 查询预处理器
```java
@Component
public class QueryPreprocessor {
    
    /**
     * 预处理自然语言查询
     */
    public ProcessedQuery preprocess(String naturalQuery) {
        return ProcessedQuery.builder()
            .originalQuery(naturalQuery)
            .cleanedQuery(cleanQuery(naturalQuery))
            .extractedEntities(extractEntities(naturalQuery))
            .queryType(classifyQueryType(naturalQuery))
            .build();
    }
    
    private String cleanQuery(String query) {
        // 清理查询文本，去除噪音
        return query.trim()
            .replaceAll("\\s+", " ")
            .toLowerCase();
    }
    
    private List<String> extractEntities(String query) {
        // 提取查询中的实体（表名、字段名等）
        // 使用NER模型或规则匹配
        return entityExtractor.extract(query);
    }
    
    private QueryType classifyQueryType(String query) {
        // 分类查询类型：SELECT、COUNT、SUM、GROUP BY等
        if (query.contains("多少") || query.contains("数量")) {
            return QueryType.COUNT;
        } else if (query.contains("总和") || query.contains("合计")) {
            return QueryType.SUM;
        } else if (query.contains("平均")) {
            return QueryType.AVERAGE;
        }
        return QueryType.SELECT;
    }
}
```

#### 2. Schema理解引擎
```java
@Component
public class SchemaUnderstandingEngine {
    
    @Autowired
    private DataSourceManager dataSourceManager;
    
    @Autowired
    private SchemaCache schemaCache;
    
    /**
     * 理解数据源Schema并建立映射
     */
    public SchemaMapping buildSchemaMapping(String dataSourceId, ProcessedQuery query) {
        Schema schema = getOrCacheSchema(dataSourceId);
        return SchemaMapping.builder()
            .schema(schema)
            .fieldMappings(mapQueryToFields(query, schema))
            .tableMappings(mapQueryToTables(query, schema))
            .relationMappings(mapQueryToRelations(query, schema))
            .build();
    }
    
    private Schema getOrCacheSchema(String dataSourceId) {
        return schemaCache.computeIfAbsent(dataSourceId, id -> {
            DataSource dataSource = dataSourceManager.getDataSource(id);
            return schemaAnalyzer.analyzeSchema(dataSource);
        });
    }
    
    private Map<String, String> mapQueryToFields(ProcessedQuery query, Schema schema) {
        Map<String, String> mappings = new HashMap<>();
        
        // 使用语义相似度匹配字段
        for (String entity : query.getExtractedEntities()) {
            String bestMatch = findBestFieldMatch(entity, schema);
            if (bestMatch != null) {
                mappings.put(entity, bestMatch);
            }
        }
        
        return mappings;
    }
    
    private String findBestFieldMatch(String entity, Schema schema) {
        // 使用向量相似度或编辑距离找到最佳匹配字段
        return schema.getFields().stream()
            .max(Comparator.comparing(field -> 
                semanticSimilarity.calculate(entity, field.getName())))
            .map(Field::getName)
            .orElse(null);
    }
}
```

#### 3. SQL生成引擎
```java
@Component
public class SQLGenerationEngine {
    
    @Autowired
    private SpringAIService springAIService;
    
    /**
     * 生成SQL语句
     */
    public GeneratedSQL generateSQL(ProcessedQuery query, SchemaMapping schemaMapping) {
        // 构建提示词
        String prompt = buildSQLPrompt(query, schemaMapping);
        
        // 使用SpringAI生成SQL
        String generatedSQL = springAIService.generateSQL(prompt);
        
        // 验证和优化SQL
        ValidatedSQL validatedSQL = validateAndOptimizeSQL(generatedSQL, schemaMapping.getSchema());
        
        return GeneratedSQL.builder()
            .originalQuery(query.getOriginalQuery())
            .generatedSQL(validatedSQL.getSql())
            .confidence(validatedSQL.getConfidence())
            .optimizationSuggestions(validatedSQL.getOptimizations())
            .build();
    }
    
    private String buildSQLPrompt(ProcessedQuery query, SchemaMapping schemaMapping) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("将以下自然语言查询转换为SQL语句：\n");
        prompt.append("查询：").append(query.getOriginalQuery()).append("\n\n");
        
        // 添加Schema信息
        prompt.append("数据库Schema：\n");
        for (Table table : schemaMapping.getSchema().getTables()) {
            prompt.append("表名：").append(table.getName()).append("\n");
            prompt.append("字段：");
            table.getFields().forEach(field -> 
                prompt.append(field.getName()).append("(").append(field.getType()).append("), "));
            prompt.append("\n\n");
        }
        
        // 添加字段映射信息
        prompt.append("字段映射：\n");
        schemaMapping.getFieldMappings().forEach((key, value) -> 
            prompt.append(key).append(" -> ").append(value).append("\n"));
        
        prompt.append("\n请生成标准的SQL语句：");
        return prompt.toString();
    }
    
    private ValidatedSQL validateAndOptimizeSQL(String sql, Schema schema) {
        // SQL语法验证
        boolean isValid = sqlValidator.validate(sql);
        
        // 性能优化建议
        List<String> optimizations = sqlOptimizer.analyze(sql, schema);
        
        // 计算置信度
        double confidence = calculateConfidence(sql, schema);
        
        return ValidatedSQL.builder()
            .sql(sql)
            .isValid(isValid)
            .confidence(confidence)
            .optimizations(optimizations)
            .build();
    }
}
```

#### 4. 查询执行引擎
```java
@Component
public class QueryExecutionEngine {
    
    @Autowired
    private DataSourceManager dataSourceManager;
    
    @Autowired
    private ConnectionPoolManager connectionPoolManager;
    
    /**
     * 执行SQL查询
     */
    public QueryResult executeQuery(GeneratedSQL generatedSQL, String dataSourceId) {
        try {
            DataSource dataSource = dataSourceManager.getDataSource(dataSourceId);
            Connection connection = connectionPoolManager.getConnection(dataSource);
            
            // 执行查询
            PreparedStatement statement = connection.prepareStatement(generatedSQL.getGeneratedSQL());
            ResultSet resultSet = statement.executeQuery();
            
            // 转换结果
            List<Map<String, Object>> results = convertResultSet(resultSet);
            
            return QueryResult.builder()
                .originalQuery(generatedSQL.getOriginalQuery())
                .executedSQL(generatedSQL.getGeneratedSQL())
                .results(results)
                .executionTime(System.currentTimeMillis())
                .success(true)
                .build();
                
        } catch (SQLException e) {
            return QueryResult.builder()
                .originalQuery(generatedSQL.getOriginalQuery())
                .executedSQL(generatedSQL.getGeneratedSQL())
                .error(e.getMessage())
                .success(false)
                .build();
        }
    }
    
    private List<Map<String, Object>> convertResultSet(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        while (resultSet.next()) {
            Map<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            results.add(row);
        }
        
        return results;
    }
}
```

## 半结构化数据处理方案

### 1. JSON文档处理
```java
@Component
public class JSONDataProcessor {

    /**
     * 将JSON文档转换为可查询的结构
     */
    public QueryableSchema processJSONDocument(String jsonContent) {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(jsonContent);

        // 分析JSON结构
        Schema schema = analyzeJSONStructure(rootNode);

        // 扁平化嵌套结构
        Map<String, Object> flattenedData = flattenJSON(rootNode);

        return QueryableSchema.builder()
            .schema(schema)
            .data(flattenedData)
            .queryLanguage("JSONPath")
            .build();
    }

    private Schema analyzeJSONStructure(JsonNode node) {
        List<Field> fields = new ArrayList<>();

        // 递归分析JSON结构
        analyzeNode("", node, fields);

        return Schema.builder()
            .name("json_document")
            .fields(fields)
            .type("JSON")
            .build();
    }

    private void analyzeNode(String prefix, JsonNode node, List<Field> fields) {
        if (node.isObject()) {
            node.fields().forEachRemaining(entry -> {
                String fieldName = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
                JsonNode value = entry.getValue();

                if (value.isValueNode()) {
                    fields.add(Field.builder()
                        .name(fieldName)
                        .type(inferJSONType(value))
                        .path(fieldName)
                        .build());
                } else {
                    analyzeNode(fieldName, value, fields);
                }
            });
        } else if (node.isArray() && node.size() > 0) {
            analyzeNode(prefix + "[*]", node.get(0), fields);
        }
    }
}
```

### 2. XML文档处理
```java
@Component
public class XMLDataProcessor {

    /**
     * 将XML文档转换为可查询的结构
     */
    public QueryableSchema processXMLDocument(String xmlContent) {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new InputSource(new StringReader(xmlContent)));

        // 分析XML结构
        Schema schema = analyzeXMLStructure(doc.getDocumentElement());

        // 转换为表格结构
        List<Map<String, Object>> tableData = convertXMLToTable(doc);

        return QueryableSchema.builder()
            .schema(schema)
            .data(tableData)
            .queryLanguage("XPath")
            .build();
    }

    private Schema analyzeXMLStructure(Element root) {
        Set<String> fieldNames = new HashSet<>();
        collectFieldNames(root, "", fieldNames);

        List<Field> fields = fieldNames.stream()
            .map(name -> Field.builder()
                .name(name)
                .type("VARCHAR")
                .xpath(name)
                .build())
            .collect(Collectors.toList());

        return Schema.builder()
            .name("xml_document")
            .fields(fields)
            .type("XML")
            .build();
    }
}
```

### 3. 半结构化数据查询转换
```java
@Component
public class SemiStructuredQueryConverter {

    /**
     * 将SQL查询转换为半结构化数据查询
     */
    public String convertToSemiStructuredQuery(String sql, QueryableSchema schema) {
        switch (schema.getQueryLanguage()) {
            case "JSONPath":
                return convertToJSONPath(sql, schema);
            case "XPath":
                return convertToXPath(sql, schema);
            case "MongoDB":
                return convertToMongoQuery(sql, schema);
            default:
                throw new UnsupportedOperationException("Unsupported query language: " + schema.getQueryLanguage());
        }
    }

    private String convertToJSONPath(String sql, QueryableSchema schema) {
        // 解析SQL语句
        SQLStatement statement = CCJSqlParserUtil.parse(sql);

        if (statement instanceof Select) {
            Select select = (Select) statement;
            PlainSelect plainSelect = (PlainSelect) select.getSelectBody();

            // 构建JSONPath查询
            StringBuilder jsonPath = new StringBuilder("$");

            // 处理WHERE条件
            if (plainSelect.getWhere() != null) {
                String whereClause = convertWhereToJSONPath(plainSelect.getWhere());
                jsonPath.append(whereClause);
            }

            return jsonPath.toString();
        }

        throw new UnsupportedOperationException("Only SELECT statements are supported");
    }

    private String convertToMongoQuery(String sql, QueryableSchema schema) {
        // 将SQL转换为MongoDB查询
        SQLStatement statement = CCJSqlParserUtil.parse(sql);

        if (statement instanceof Select) {
            Select select = (Select) statement;
            PlainSelect plainSelect = (PlainSelect) select.getSelectBody();

            // 构建MongoDB查询文档
            Document query = new Document();

            // 处理WHERE条件
            if (plainSelect.getWhere() != null) {
                Document whereDoc = convertWhereToMongo(plainSelect.getWhere());
                query.putAll(whereDoc);
            }

            return query.toJson();
        }

        throw new UnsupportedOperationException("Only SELECT statements are supported");
    }
}
```

## NoSQL数据库选型方案

### 1. MongoDB - 文档数据库
```yaml
# 适用场景
use_cases:
  - JSON文档存储和查询
  - 灵活的Schema设计
  - 复杂的嵌套数据结构
  - 地理空间数据查询

# 优势
advantages:
  - 原生JSON支持
  - 强大的聚合框架
  - 水平扩展能力
  - 丰富的查询操作符

# Text2SQL集成
integration:
  query_language: "MongoDB Query Language"
  aggregation_support: true
  index_optimization: true
```

### 2. Elasticsearch - 搜索引擎
```yaml
# 适用场景
use_cases:
  - 全文搜索和分析
  - 日志数据分析
  - 实时数据聚合
  - 复杂的过滤和排序

# 优势
advantages:
  - 强大的全文搜索
  - 实时分析能力
  - RESTful API
  - 丰富的聚合功能

# Text2SQL集成
integration:
  query_language: "Elasticsearch DSL"
  aggregation_support: true
  full_text_search: true
```

### 3. Redis - 键值数据库
```yaml
# 适用场景
use_cases:
  - 缓存数据查询
  - 会话数据存储
  - 计数器和排行榜
  - 实时数据处理

# 优势
advantages:
  - 极高的性能
  - 丰富的数据类型
  - 原子操作支持
  - 发布订阅功能

# Text2SQL集成
integration:
  query_language: "Redis Commands"
  data_structures: ["String", "Hash", "List", "Set", "ZSet"]
  lua_script_support: true
```

### 4. NoSQL查询适配器
```java
@Component
public class NoSQLQueryAdapter {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 根据数据源类型执行查询
     */
    public QueryResult executeNoSQLQuery(String query, String dataSourceType, String collection) {
        switch (dataSourceType.toLowerCase()) {
            case "mongodb":
                return executeMongoQuery(query, collection);
            case "elasticsearch":
                return executeElasticsearchQuery(query, collection);
            case "redis":
                return executeRedisQuery(query, collection);
            default:
                throw new UnsupportedOperationException("Unsupported NoSQL database: " + dataSourceType);
        }
    }

    private QueryResult executeMongoQuery(String query, String collection) {
        try {
            // 解析MongoDB查询
            Document queryDoc = Document.parse(query);

            // 执行查询
            List<Document> results = mongoTemplate.find(
                new Query(Criteria.fromDocument(queryDoc)),
                Document.class,
                collection
            );

            // 转换结果
            List<Map<String, Object>> convertedResults = results.stream()
                .map(doc -> new HashMap<String, Object>(doc))
                .collect(Collectors.toList());

            return QueryResult.builder()
                .results(convertedResults)
                .success(true)
                .executionTime(System.currentTimeMillis())
                .build();

        } catch (Exception e) {
            return QueryResult.builder()
                .error(e.getMessage())
                .success(false)
                .build();
        }
    }

    private QueryResult executeElasticsearchQuery(String query, String index) {
        try {
            // 构建Elasticsearch查询
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(QueryBuilders.wrapperQuery(query));
            searchRequest.source(sourceBuilder);

            // 执行查询
            SearchResponse response = elasticsearchTemplate.search(searchRequest, RequestOptions.DEFAULT);

            // 转换结果
            List<Map<String, Object>> results = Arrays.stream(response.getHits().getHits())
                .map(hit -> hit.getSourceAsMap())
                .collect(Collectors.toList());

            return QueryResult.builder()
                .results(results)
                .success(true)
                .totalHits(response.getHits().getTotalHits().value)
                .build();

        } catch (Exception e) {
            return QueryResult.builder()
                .error(e.getMessage())
                .success(false)
                .build();
        }
    }
}
```

## SpringAI集成方案

### 1. SpringAI配置
```java
@Configuration
@EnableConfigurationProperties(Text2SQLProperties.class)
public class Text2SQLConfiguration {
    
    @Bean
    public ChatClient chatClient(ChatClient.Builder builder) {
        return builder
            .defaultSystem("你是一个专业的SQL生成助手，能够将自然语言查询转换为准确的SQL语句。")
            .build();
    }
    
    @Bean
    public SpringAIService springAIService(ChatClient chatClient) {
        return new SpringAIService(chatClient);
    }
}

@ConfigurationProperties(prefix = "text2sql.ai")
@Data
public class Text2SQLProperties {
    private String model = "gpt-4";
    private double temperature = 0.1;
    private int maxTokens = 1000;
    private boolean enableOptimization = true;
}
```

### 2. SpringAI Text2SQL完整实现案例

#### 2.1 核心配置类
```java
@Configuration
@EnableConfigurationProperties(Text2SQLProperties.class)
public class Text2SQLConfiguration {

    @Bean
    public ChatClient chatClient(ChatClient.Builder builder, Text2SQLProperties properties) {
        return builder
            .defaultSystem("""
                你是一个专业的SQL生成助手，能够将自然语言查询转换为准确的SQL语句。

                规则：
                1. 只生成SELECT语句，不生成INSERT、UPDATE、DELETE语句
                2. 使用标准SQL语法
                3. 字段名和表名使用反引号包围
                4. 对于模糊查询使用LIKE操作符
                5. 对于数值比较使用适当的操作符（>、<、=、>=、<=）
                6. 对于日期查询使用DATE函数
                7. 结果限制使用LIMIT子句

                返回格式：只返回SQL语句，不要包含任何解释文字。
                """)
            .defaultOptions(ChatOptionsBuilder.builder()
                .withModel(properties.getModel())
                .withTemperature(properties.getTemperature())
                .withMaxTokens(properties.getMaxTokens())
                .build())
            .build();
    }

    @Bean
    public EmbeddingClient embeddingClient() {
        return new OpenAiEmbeddingClient(new OpenAiApi(System.getenv("OPENAI_API_KEY")));
    }

    @Bean
    public VectorStore vectorStore(EmbeddingClient embeddingClient) {
        return new SimpleVectorStore(embeddingClient);
    }
}
```

#### 2.2 表结构向量化服务
```java
@Service
public class SchemaVectorService {

    private final EmbeddingClient embeddingClient;
    private final VectorStore vectorStore;

    public SchemaVectorService(EmbeddingClient embeddingClient, VectorStore vectorStore) {
        this.embeddingClient = embeddingClient;
        this.vectorStore = vectorStore;
    }

    /**
     * 将表结构信息向量化并存储
     */
    public void vectorizeSchema(Schema schema) {
        for (Table table : schema.getTables()) {
            // 构建表结构描述
            String tableDescription = buildTableDescription(table);

            // 创建文档
            Document document = new Document(tableDescription);
            document.getMetadata().put("table_name", table.getName());
            document.getMetadata().put("table_type", table.getType());
            document.getMetadata().put("field_count", table.getFields().size());

            // 添加字段信息
            for (Field field : table.getFields()) {
                document.getMetadata().put("field_" + field.getName(), field.getType());
            }

            // 存储到向量数据库
            vectorStore.add(List.of(document));
        }
    }

    /**
     * 根据查询检索最相关的表结构
     */
    public List<Table> searchRelevantTables(String query, int topK) {
        // 向量检索
        List<Document> similarDocs = vectorStore.similaritySearch(
            SearchRequest.query(query).withTopK(topK)
        );

        // 转换为表结构对象
        return similarDocs.stream()
            .map(this::documentToTable)
            .collect(Collectors.toList());
    }

    private String buildTableDescription(Table table) {
        StringBuilder description = new StringBuilder();
        description.append("表名: ").append(table.getName()).append("\n");
        description.append("表描述: ").append(table.getComment()).append("\n");
        description.append("字段信息:\n");

        for (Field field : table.getFields()) {
            description.append("- ").append(field.getName())
                .append(" (").append(field.getType()).append(")")
                .append(": ").append(field.getComment()).append("\n");
        }

        return description.toString();
    }

    private Table documentToTable(Document document) {
        String tableName = (String) document.getMetadata().get("table_name");
        String tableType = (String) document.getMetadata().get("table_type");

        List<Field> fields = document.getMetadata().entrySet().stream()
            .filter(entry -> entry.getKey().startsWith("field_"))
            .map(entry -> {
                String fieldName = entry.getKey().substring(6); // 移除"field_"前缀
                String fieldType = (String) entry.getValue();
                return Field.builder()
                    .name(fieldName)
                    .type(fieldType)
                    .build();
            })
            .collect(Collectors.toList());

        return Table.builder()
            .name(tableName)
            .type(tableType)
            .fields(fields)
            .build();
    }
}
```

#### 2.3 Text2SQL核心服务
```java
@Service
public class Text2SQLService {

    private final ChatClient chatClient;
    private final SchemaVectorService schemaVectorService;
    private final QueryExecutionEngine executionEngine;

    public Text2SQLService(ChatClient chatClient,
                          SchemaVectorService schemaVectorService,
                          QueryExecutionEngine executionEngine) {
        this.chatClient = chatClient;
        this.schemaVectorService = schemaVectorService;
        this.executionEngine = executionEngine;
    }

    /**
     * 完整的Text2SQL处理流程
     */
    public Text2SQLResult processQuery(String naturalQuery, String dataSourceId) {
        try {
            // 1. 检索最相关的表结构
            List<Table> relevantTables = schemaVectorService.searchRelevantTables(naturalQuery, 3);

            // 2. 构建增强的提示词
            String enhancedPrompt = buildEnhancedPrompt(naturalQuery, relevantTables);

            // 3. 使用SpringAI生成SQL
            String generatedSQL = chatClient.prompt()
                .user(enhancedPrompt)
                .call()
                .content();

            // 4. 清理和验证SQL
            String cleanSQL = cleanGeneratedSQL(generatedSQL);

            // 5. 执行SQL查询
            QueryResult queryResult = executionEngine.executeQuery(cleanSQL, dataSourceId);

            // 6. 生成自然语言回答
            String naturalAnswer = generateNaturalAnswer(naturalQuery, queryResult);

            return Text2SQLResult.builder()
                .originalQuery(naturalQuery)
                .generatedSQL(cleanSQL)
                .queryResult(queryResult)
                .naturalAnswer(naturalAnswer)
                .relevantTables(relevantTables)
                .success(true)
                .build();

        } catch (Exception e) {
            return Text2SQLResult.builder()
                .originalQuery(naturalQuery)
                .error(e.getMessage())
                .success(false)
                .build();
        }
    }

    private String buildEnhancedPrompt(String naturalQuery, List<Table> relevantTables) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("根据以下数据库表结构，将自然语言查询转换为SQL语句：\n\n");
        prompt.append("自然语言查询：").append(naturalQuery).append("\n\n");

        prompt.append("可用的数据库表结构：\n");
        for (Table table : relevantTables) {
            prompt.append("表名：").append(table.getName()).append("\n");
            prompt.append("字段：\n");
            for (Field field : table.getFields()) {
                prompt.append("  - ").append(field.getName())
                    .append(" (").append(field.getType()).append(")\n");
            }
            prompt.append("\n");
        }

        // 添加示例
        prompt.append("示例：\n");
        prompt.append("查询：查找销售额最高的前10个产品\n");
        prompt.append("SQL：SELECT product_name, sales_amount FROM products ORDER BY sales_amount DESC LIMIT 10;\n\n");

        prompt.append("请生成对应的SQL语句：");

        return prompt.toString();
    }

    private String cleanGeneratedSQL(String generatedSQL) {
        // 移除可能的markdown标记
        String cleaned = generatedSQL.replaceAll("```sql", "").replaceAll("```", "");

        // 移除多余的空白字符
        cleaned = cleaned.trim();

        // 确保以分号结尾
        if (!cleaned.endsWith(";")) {
            cleaned += ";";
        }

        return cleaned;
    }

    /**
     * 将查询结果转换为自然语言回答
     */
    private String generateNaturalAnswer(String originalQuery, QueryResult queryResult) {
        if (!queryResult.isSuccess() || queryResult.getResults().isEmpty()) {
            return "抱歉，没有找到相关数据。";
        }

        StringBuilder answerPrompt = new StringBuilder();
        answerPrompt.append("根据以下查询结果，用自然语言回答用户的问题：\n\n");
        answerPrompt.append("用户问题：").append(originalQuery).append("\n\n");
        answerPrompt.append("查询结果：\n");

        // 添加查询结果（限制数量避免token过多）
        List<Map<String, Object>> results = queryResult.getResults();
        int maxResults = Math.min(results.size(), 10);

        for (int i = 0; i < maxResults; i++) {
            Map<String, Object> row = results.get(i);
            answerPrompt.append("第").append(i + 1).append("条：");
            row.forEach((key, value) ->
                answerPrompt.append(key).append("=").append(value).append(", "));
            answerPrompt.append("\n");
        }

        if (results.size() > maxResults) {
            answerPrompt.append("... 还有").append(results.size() - maxResults).append("条记录\n");
        }

        answerPrompt.append("\n请用简洁、友好的自然语言总结这些结果：");

        return chatClient.prompt()
            .user(answerPrompt.toString())
            .call()
            .content();
    }
}
```

#### 2.4 REST API控制器
```java
@RestController
@RequestMapping("/api/text2sql")
@Validated
public class Text2SQLController {

    private final Text2SQLService text2sqlService;

    public Text2SQLController(Text2SQLService text2sqlService) {
        this.text2sqlService = text2sqlService;
    }

    @PostMapping("/query")
    public ResponseEntity<Text2SQLResponse> processQuery(@RequestBody @Valid Text2SQLRequest request) {
        Text2SQLResult result = text2sqlService.processQuery(
            request.getQuery(),
            request.getDataSourceId()
        );

        Text2SQLResponse response = Text2SQLResponse.builder()
            .originalQuery(result.getOriginalQuery())
            .generatedSQL(result.getGeneratedSQL())
            .naturalAnswer(result.getNaturalAnswer())
            .resultCount(result.getQueryResult() != null ? result.getQueryResult().getResults().size() : 0)
            .executionTime(result.getQueryResult() != null ? result.getQueryResult().getExecutionTime() : 0)
            .success(result.isSuccess())
            .error(result.getError())
            .build();

        return ResponseEntity.ok(response);
    }

    @PostMapping("/schema/vectorize")
    public ResponseEntity<String> vectorizeSchema(@RequestParam String dataSourceId) {
        // 触发Schema向量化
        // 实现省略...
        return ResponseEntity.ok("Schema vectorization completed");
    }
}

// 请求和响应DTO
@Data
@Builder
public class Text2SQLRequest {
    @NotBlank(message = "查询不能为空")
    private String query;

    @NotBlank(message = "数据源ID不能为空")
    private String dataSourceId;
}

@Data
@Builder
public class Text2SQLResponse {
    private String originalQuery;
    private String generatedSQL;
    private String naturalAnswer;
    private int resultCount;
    private long executionTime;
    private boolean success;
    private String error;
}
```

#### 2.5 使用示例
```java
@Component
public class Text2SQLExample {

    @Autowired
    private Text2SQLService text2sqlService;

    public void demonstrateUsage() {
        // 示例1：简单查询
        String query1 = "查询销售额最高的前10个产品";
        Text2SQLResult result1 = text2sqlService.processQuery(query1, "mysql_sales_db");

        System.out.println("原始查询: " + result1.getOriginalQuery());
        System.out.println("生成的SQL: " + result1.getGeneratedSQL());
        System.out.println("自然语言回答: " + result1.getNaturalAnswer());

        // 示例2：复杂查询
        String query2 = "统计每个地区2023年的总销售额，按销售额降序排列";
        Text2SQLResult result2 = text2sqlService.processQuery(query2, "mysql_sales_db");

        // 示例3：条件查询
        String query3 = "查找价格在100到500之间的所有产品";
        Text2SQLResult result3 = text2sqlService.processQuery(query3, "mysql_product_db");
    }
}
```

## 数据存储方案

### 1. 多数据源管理
```java
@Component
public class DataSourceManager {
    
    private final Map<String, DataSource> dataSources = new ConcurrentHashMap<>();
    
    /**
     * 注册CSV数据源
     */
    public void registerCSVDataSource(String id, String filePath) {
        CSVDataSource csvDataSource = new CSVDataSource(filePath);
        dataSources.put(id, csvDataSource);
    }
    
    /**
     * 注册Excel数据源
     */
    public void registerExcelDataSource(String id, String filePath, String sheetName) {
        ExcelDataSource excelDataSource = new ExcelDataSource(filePath, sheetName);
        dataSources.put(id, excelDataSource);
    }
    
    /**
     * 注册数据库数据源
     */
    public void registerDatabaseDataSource(String id, String jdbcUrl, String username, String password) {
        DatabaseDataSource dbDataSource = new DatabaseDataSource(jdbcUrl, username, password);
        dataSources.put(id, dbDataSource);
    }
    
    public DataSource getDataSource(String id) {
        return dataSources.get(id);
    }
}
```

### 2. 文件数据源实现
```java
public class CSVDataSource implements DataSource {
    
    private final String filePath;
    private Schema schema;
    
    public CSVDataSource(String filePath) {
        this.filePath = filePath;
        this.schema = analyzeCSVSchema();
    }
    
    private Schema analyzeCSVSchema() {
        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            String[] headers = reader.readNext();
            String[] firstRow = reader.readNext();
            
            List<Field> fields = new ArrayList<>();
            for (int i = 0; i < headers.length; i++) {
                String fieldName = headers[i];
                String fieldType = inferDataType(firstRow[i]);
                fields.add(new Field(fieldName, fieldType));
            }
            
            return Schema.builder()
                .tables(List.of(Table.builder()
                    .name("csv_data")
                    .fields(fields)
                    .build()))
                .build();
                
        } catch (IOException e) {
            throw new RuntimeException("Failed to analyze CSV schema", e);
        }
    }
    
    private String inferDataType(String value) {
        if (value == null || value.isEmpty()) {
            return "VARCHAR";
        }
        
        try {
            Integer.parseInt(value);
            return "INTEGER";
        } catch (NumberFormatException e1) {
            try {
                Double.parseDouble(value);
                return "DOUBLE";
            } catch (NumberFormatException e2) {
                return "VARCHAR";
            }
        }
    }
    
    public List<Map<String, Object>> query(String sql) {
        // 使用H2内存数据库执行CSV查询
        return executeCSVQuery(sql);
    }
}
```

### 3. 缓存策略
```java
@Component
public class SchemaCache {
    
    private final Cache<String, Schema> schemaCache;
    private final Cache<String, List<Map<String, Object>>> queryCache;
    
    public SchemaCache() {
        this.schemaCache = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
            
        this.queryCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();
    }
    
    public Schema computeIfAbsent(String dataSourceId, Function<String, Schema> loader) {
        return schemaCache.get(dataSourceId, loader);
    }
    
    public List<Map<String, Object>> getCachedQuery(String queryKey) {
        return queryCache.getIfPresent(queryKey);
    }
    
    public void cacheQuery(String queryKey, List<Map<String, Object>> result) {
        queryCache.put(queryKey, result);
    }
}
```

## 配置参数

```yaml
text2sql:
  enabled: true

  # SpringAI配置
  ai:
    model: "gpt-4"
    temperature: 0.1
    max-tokens: 1000
    enable-optimization: true

  # 向量检索配置
  vector:
    embedding-model: "text-embedding-ada-002"
    vector-dimension: 1536
    similarity-threshold: 0.7
    max-relevant-tables: 3

  # 数据源配置
  datasources:
    default-timeout: 30s
    max-connections: 20
    connection-pool-size: 10

    # 支持的数据源类型
    supported-types:
      - mysql
      - postgresql
      - mongodb
      - elasticsearch
      - redis
      - csv
      - excel
      - json
      - xml

  # 半结构化数据配置
  semi-structured:
    json:
      max-depth: 10
      flatten-arrays: true
      preserve-types: true
    xml:
      namespace-aware: true
      validate-schema: false
      max-elements: 10000
    csv:
      auto-detect-delimiter: true
      header-row: true
      encoding: "UTF-8"

  # NoSQL配置
  nosql:
    mongodb:
      aggregation-timeout: 60s
      max-aggregation-stages: 20
    elasticsearch:
      max-search-size: 10000
      scroll-timeout: 5m
    redis:
      command-timeout: 10s
      max-pipeline-size: 100

  # 缓存配置
  cache:
    schema-cache-size: 100
    schema-cache-ttl: 1h
    query-cache-size: 1000
    query-cache-ttl: 30m
    vector-cache-size: 500
    vector-cache-ttl: 2h

  # 查询配置
  query:
    max-result-size: 1000
    enable-query-optimization: true
    enable-sql-validation: true
    confidence-threshold: 0.7
    enable-natural-answer: true
    max-answer-length: 500

  # 监控配置
  monitoring:
    enable-performance-monitoring: true
    enable-query-logging: true
    slow-query-threshold: 5s
    enable-vector-search-metrics: true
```

## 集成接口

### 1. Text2SQL检索接口
```java
@RestController
@RequestMapping("/api/text2sql")
public class Text2SQLController {
    
    @Autowired
    private Text2SQLRetrievalService text2sqlService;
    
    @PostMapping("/query")
    public ResponseEntity<Text2SQLResult> query(@RequestBody Text2SQLRequest request) {
        Text2SQLResult result = text2sqlService.processQuery(
            request.getNaturalQuery(),
            request.getDataSourceId()
        );
        return ResponseEntity.ok(result);
    }
    
    @PostMapping("/explain")
    public ResponseEntity<String> explainSQL(@RequestBody SQLExplainRequest request) {
        String explanation = text2sqlService.explainSQL(request.getSql());
        return ResponseEntity.ok(explanation);
    }
}
```

### 2. 与检索模块集成
```java
@Component
public class Text2SQLRetrievalService {
    
    @Autowired
    private QueryPreprocessor queryPreprocessor;
    
    @Autowired
    private SchemaUnderstandingEngine schemaEngine;
    
    @Autowired
    private SQLGenerationEngine sqlEngine;
    
    @Autowired
    private QueryExecutionEngine executionEngine;
    
    /**
     * 处理Text2SQL查询
     */
    public Text2SQLResult processQuery(String naturalQuery, String dataSourceId) {
        // 1. 查询预处理
        ProcessedQuery processedQuery = queryPreprocessor.preprocess(naturalQuery);
        
        // 2. Schema理解与映射
        SchemaMapping schemaMapping = schemaEngine.buildSchemaMapping(dataSourceId, processedQuery);
        
        // 3. SQL生成
        GeneratedSQL generatedSQL = sqlEngine.generateSQL(processedQuery, schemaMapping);
        
        // 4. 查询执行
        QueryResult queryResult = executionEngine.executeQuery(generatedSQL, dataSourceId);
        
        // 5. 结果封装
        return Text2SQLResult.builder()
            .originalQuery(naturalQuery)
            .generatedSQL(generatedSQL.getGeneratedSQL())
            .queryResult(queryResult)
            .confidence(generatedSQL.getConfidence())
            .executionTime(queryResult.getExecutionTime())
            .build();
    }
}
```

## 监控与优化

### 1. 性能监控
```java
@Component
public class Text2SQLMonitor {
    
    private final MeterRegistry meterRegistry;
    
    public Text2SQLMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    public void recordQueryExecution(String dataSourceId, long executionTime, boolean success) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("text2sql.query.execution")
            .tag("datasource", dataSourceId)
            .tag("success", String.valueOf(success))
            .register(meterRegistry));
            
        meterRegistry.counter("text2sql.query.count",
            "datasource", dataSourceId,
            "success", String.valueOf(success))
            .increment();
    }
    
    public void recordSQLGeneration(double confidence, long generationTime) {
        meterRegistry.gauge("text2sql.sql.confidence", confidence);
        meterRegistry.timer("text2sql.sql.generation").record(generationTime, TimeUnit.MILLISECONDS);
    }
}
```

### 2. 错误处理
```java
@Component
public class Text2SQLErrorHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(Text2SQLErrorHandler.class);
    
    public Text2SQLResult handleError(Exception e, String naturalQuery) {
        logger.error("Text2SQL processing failed for query: {}", naturalQuery, e);
        
        return Text2SQLResult.builder()
            .originalQuery(naturalQuery)
            .error(e.getMessage())
            .success(false)
            .build();
    }
    
    public String generateFallbackSQL(String naturalQuery, Schema schema) {
        // 生成简单的fallback SQL
        return "SELECT * FROM " + schema.getTables().get(0).getName() + " LIMIT 10";
    }
}
```
